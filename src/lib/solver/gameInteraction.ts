/**
 * Game interaction utilities for the Letters website
 *
 * This module handles playing words, undoing moves, and other game interactions
 * using Playwright automation.
 */

import type { Page } from '@playwright/test';
import type { Position } from '../types';

/**
 * Configuration for game interaction behavior
 */
interface InteractionConfig {
	/** Delay between tile clicks (ms) */
	clickDelay: number;
	/** Timeout for waiting for elements (ms) */
	timeout: number;
	/** Delay after submitting word (ms) */
	submitDelay: number;
	/** Maximum retries for failed interactions */
	maxRetries: number;
	/** Whether to take screenshots for debugging */
	debugScreenshots: boolean;
}

/**
 * Default interaction configuration
 */
const DEFAULT_CONFIG: InteractionConfig = {
	clickDelay: 150,
	timeout: 5000,
	submitDelay: 2000,
	maxRetries: 3,
	debugScreenshots: false
};

/**
 * Possible selectors for game interaction elements
 */
const SELECTORS = {
	// Tile selectors (in order of preference)
	tiles: [
		'[data-row="{row}"][data-col="{col}"]',
		'[data-position="{row}-{col}"]',
		'.tile[data-row="{row}"][data-col="{col}"]',
		'.game-tile:nth-child({index})',
		'.tile:nth-child({index})'
	],

	// Submit button selectors
	submit: [
		'.submit-button',
		'#submit-word',
		'button[type="submit"]',
		'.play-word',
		'.confirm-word',
		'[data-testid="submit"]'
	],

	// Clear/reset selectors
	clear: [
		'.clear-selection',
		'.reset-word',
		'.undo-selection',
		'#clear-word',
		'[data-testid="clear"]'
	],

	// Undo button selectors
	undo: ['.undo-button', '.undo-move', '#undo', '[data-testid="undo"]'],

	// Word display selectors
	wordDisplay: ['.current-word', '.selected-word', '.word-display', '#current-word']
};

/**
 * Play a word by clicking tiles in sequence and submitting
 */
export async function playWord(
	page: Page,
	positions: Position[],
	config: InteractionConfig = DEFAULT_CONFIG
): Promise<void> {
	if (positions.length === 0) {
		throw new Error('No positions provided for word');
	}

	console.log(`[playWord] Playing word with ${positions.length} tiles`);

	let attempt = 0;
	while (attempt < config.maxRetries) {
		try {
			// Clear any existing selection first
			await clearSelection(page, config);

			// Click tiles in sequence
			await clickTileSequence(page, positions, config);

			// Verify word is selected
			await verifyWordSelection(page, positions, config);

			// Submit the word
			await submitWord(page, config);

			// Wait for game to process the move
			await waitForMoveProcessing(page, config);

			console.log(`[playWord] Successfully played word`);
			return;
		} catch (error) {
			attempt++;
			console.warn(`[playWord] Attempt ${attempt} failed:`, error);

			if (config.debugScreenshots) {
				await page.screenshot({
					path: `debug-play-word-attempt-${attempt}.png`,
					fullPage: true
				});
			}

			if (attempt >= config.maxRetries) {
				throw new Error(
					`Failed to play word after ${config.maxRetries} attempts. Last error: ${(error as Error).message}`
				);
			}

			// Wait before retry
			await page.waitForTimeout(1000);
		}
	}
}

/**
 * Clear any existing tile selection
 */
async function clearSelection(page: Page, config: InteractionConfig): Promise<void> {
	for (const selector of SELECTORS.clear) {
		try {
			const element = await page.$(selector);
			if (element) {
				await element.click();
				await page.waitForTimeout(config.clickDelay);
				console.log(`[clearSelection] Cleared selection using: ${selector}`);
				return;
			}
		} catch (error) {
			// Try next selector
		}
	}

	// If no clear button found, try clicking outside the game area
	try {
		await page.click('body', { position: { x: 10, y: 10 } });
		await page.waitForTimeout(config.clickDelay);
	} catch (error) {
		console.warn('[clearSelection] Could not clear selection');
	}
}

/**
 * Click tiles in the specified sequence
 */
async function clickTileSequence(
	page: Page,
	positions: Position[],
	config: InteractionConfig
): Promise<void> {
	for (let i = 0; i < positions.length; i++) {
		const [row, col] = positions[i];
		console.log(
			`[clickTileSequence] Clicking tile [${row}, ${col}] (${i + 1}/${positions.length})`
		);

		await clickTile(page, row, col, config);

		// Add delay between clicks
		if (i < positions.length - 1) {
			await page.waitForTimeout(config.clickDelay);
		}
	}
}

/**
 * Click a specific tile at the given position
 */
async function clickTile(
	page: Page,
	row: number,
	col: number,
	config: InteractionConfig
): Promise<void> {
	const index = row * 5 + col + 1; // 1-based index for nth-child

	for (const selectorTemplate of SELECTORS.tiles) {
		const selector = selectorTemplate
			.replace('{row}', row.toString())
			.replace('{col}', col.toString())
			.replace('{index}', index.toString());

		try {
			const element = await page.waitForSelector(selector, {
				timeout: config.timeout / SELECTORS.tiles.length
			});
			if (element) {
				// Ensure element is visible and clickable
				await element.scrollIntoViewIfNeeded();
				await element.click();

				console.log(`[clickTile] Clicked tile [${row}, ${col}] using: ${selector}`);
				return;
			}
		} catch (error) {
			// Try next selector
		}
	}

	throw new Error(`Could not find clickable tile at position [${row}, ${col}]`);
}

/**
 * Verify that the word selection is correct
 */
async function verifyWordSelection(
	page: Page,
	positions: Position[],
	config: InteractionConfig
): Promise<void> {
	// Try to find word display element
	for (const selector of SELECTORS.wordDisplay) {
		try {
			const element = await page.$(selector);
			if (element) {
				const displayedWord = await element.textContent();
				console.log(`[verifyWordSelection] Displayed word: "${displayedWord}"`);

				// Basic verification - check that something is displayed
				if (displayedWord && displayedWord.trim().length >= positions.length) {
					return;
				}
			}
		} catch (error) {
			// Try next selector
		}
	}

	// Fallback: Check if tiles appear selected
	let selectedCount = 0;
	for (const [row, col] of positions) {
		try {
			const index = row * 5 + col + 1;
			const selectors = [
				`[data-row="${row}"][data-col="${col}"].selected`,
				`[data-row="${row}"][data-col="${col}"].highlighted`,
				`.tile:nth-child(${index}).selected`,
				`.tile:nth-child(${index}).active`
			];

			for (const selector of selectors) {
				const element = await page.$(selector);
				if (element) {
					selectedCount++;
					break;
				}
			}
		} catch (error) {
			// Continue checking other tiles
		}
	}

	if (selectedCount < positions.length) {
		console.warn(
			`[verifyWordSelection] Only ${selectedCount}/${positions.length} tiles appear selected`
		);
	}
}

/**
 * Submit the currently selected word
 */
async function submitWord(page: Page, config: InteractionConfig): Promise<void> {
	for (const selector of SELECTORS.submit) {
		try {
			const element = await page.waitForSelector(selector, {
				timeout: config.timeout / SELECTORS.submit.length
			});
			if (element) {
				// Check if button is enabled
				const isDisabled = await element.getAttribute('disabled');
				if (isDisabled) {
					throw new Error('Submit button is disabled');
				}

				await element.click();
				console.log(`[submitWord] Submitted word using: ${selector}`);
				return;
			}
		} catch (error) {
			// Try next selector
		}
	}

	// Fallback: Try pressing Enter
	try {
		await page.keyboard.press('Enter');
		console.log('[submitWord] Submitted word using Enter key');
	} catch (error) {
		throw new Error('Could not find submit button or submit word');
	}
}

/**
 * Wait for the game to process the move
 */
async function waitForMoveProcessing(page: Page, config: InteractionConfig): Promise<void> {
	// Wait for submit delay
	await page.waitForTimeout(config.submitDelay);

	// Wait for any loading indicators to disappear
	const loadingSelectors = ['.loading', '.processing', '.spinner', '[data-loading="true"]'];

	for (const selector of loadingSelectors) {
		try {
			await page.waitForSelector(selector, { state: 'hidden', timeout: 1000 });
		} catch (error) {
			// Loading indicator might not exist, continue
		}
	}

	// Ensure page is ready for next interaction
	await page.waitForFunction(() => document.readyState === 'complete');
}

/**
 * Undo strategies for different scenarios
 */
enum UndoStrategy {
	BUTTON = 'button',
	KEYBOARD = 'keyboard',
	RELOAD = 'reload',
	NAVIGATION = 'navigation'
}

/**
 * Result of an undo operation
 */
interface UndoResult {
	success: boolean;
	strategy: UndoStrategy;
	message: string;
}

/**
 * Undo the last move with multiple fallback strategies
 */
export async function undoLastMove(
	page: Page,
	config: InteractionConfig = DEFAULT_CONFIG
): Promise<UndoResult> {
	console.log('[undoLastMove] Attempting to undo last move');

	// Strategy 1: Try undo button
	const buttonResult = await tryUndoButton(page, config);
	if (buttonResult.success) {
		return buttonResult;
	}

	// Strategy 2: Try keyboard shortcuts
	const keyboardResult = await tryUndoKeyboard(page, config);
	if (keyboardResult.success) {
		return keyboardResult;
	}

	// Strategy 3: Try browser navigation (back button)
	const navigationResult = await tryUndoNavigation(page, config);
	if (navigationResult.success) {
		return navigationResult;
	}

	// Strategy 4: Fallback to page reload
	const reloadResult = await tryUndoReload(page, config);
	return reloadResult;
}

/**
 * Try to undo using an undo button
 */
async function tryUndoButton(page: Page, config: InteractionConfig): Promise<UndoResult> {
	for (const selector of SELECTORS.undo) {
		try {
			const element = await page.waitForSelector(selector, {
				timeout: config.timeout / SELECTORS.undo.length
			});

			if (element) {
				// Check if button is enabled
				const isDisabled = await element.getAttribute('disabled');
				if (isDisabled) {
					continue; // Try next selector
				}

				await element.click();
				await page.waitForTimeout(config.submitDelay);

				// Verify undo worked by checking if game state changed
				const verified = await verifyUndoSuccess(page, config);

				if (verified) {
					console.log(`[undoLastMove] Undid move using button: ${selector}`);
					return {
						success: true,
						strategy: UndoStrategy.BUTTON,
						message: `Successfully undid using button: ${selector}`
					};
				}
			}
		} catch (error) {
			// Try next selector
		}
	}

	return {
		success: false,
		strategy: UndoStrategy.BUTTON,
		message: 'No working undo button found'
	};
}

/**
 * Try to undo using keyboard shortcuts
 */
async function tryUndoKeyboard(page: Page, config: InteractionConfig): Promise<UndoResult> {
	const shortcuts = [
		'Control+Z',
		'Meta+Z', // Mac
		'Escape',
		'Backspace'
	];

	for (const shortcut of shortcuts) {
		try {
			await page.keyboard.press(shortcut);
			await page.waitForTimeout(config.submitDelay);

			const verified = await verifyUndoSuccess(page, config);
			if (verified) {
				console.log(`[undoLastMove] Undid move using keyboard: ${shortcut}`);
				return {
					success: true,
					strategy: UndoStrategy.KEYBOARD,
					message: `Successfully undid using keyboard: ${shortcut}`
				};
			}
		} catch (error) {
			// Try next shortcut
		}
	}

	return {
		success: false,
		strategy: UndoStrategy.KEYBOARD,
		message: 'No working keyboard shortcuts found'
	};
}

/**
 * Try to undo using browser navigation
 */
async function tryUndoNavigation(page: Page, config: InteractionConfig): Promise<UndoResult> {
	try {
		// Check if we can go back
		const canGoBack = await page.evaluate(() => window.history.length > 1);

		if (canGoBack) {
			await page.goBack();
			await page.waitForTimeout(config.submitDelay);

			// Wait for game to load
			await waitForGameReady(page, config);

			console.log('[undoLastMove] Undid move using browser back');
			return {
				success: true,
				strategy: UndoStrategy.NAVIGATION,
				message: 'Successfully undid using browser navigation'
			};
		}
	} catch (error) {
		// Navigation failed
	}

	return {
		success: false,
		strategy: UndoStrategy.NAVIGATION,
		message: 'Browser navigation not available'
	};
}

/**
 * Fallback: Reload the page to reset state
 */
async function tryUndoReload(page: Page, config: InteractionConfig): Promise<UndoResult> {
	try {
		console.warn('[undoLastMove] Using page reload as fallback');

		await page.reload();
		await waitForGameReady(page, config);

		return {
			success: true,
			strategy: UndoStrategy.RELOAD,
			message: 'Reset game state by reloading page'
		};
	} catch (error) {
		return {
			success: false,
			strategy: UndoStrategy.RELOAD,
			message: `Page reload failed: ${(error as Error).message}`
		};
	}
}

/**
 * Verify that the undo operation was successful
 */
async function verifyUndoSuccess(page: Page, config: InteractionConfig): Promise<boolean> {
	try {
		// Check if any word is currently selected (should be cleared after undo)
		const currentWord = await getCurrentWord(page);
		if (currentWord && currentWord.length > 0) {
			return false; // Word still selected, undo might not have worked
		}

		// Check if game is ready for new interactions
		const gameReady = await isGameReady(page);
		return gameReady;
	} catch (error) {
		return false;
	}
}

/**
 * Advanced undo with state restoration
 */
export async function undoToState(
	page: Page,
	targetTurn: number,
	config: InteractionConfig = DEFAULT_CONFIG
): Promise<UndoResult> {
	console.log(`[undoToState] Attempting to undo to turn ${targetTurn}`);

	// This would require the game to support multi-level undo
	// For now, we can only undo one move at a time

	let currentTurn = await getCurrentTurn(page);
	let undoCount = 0;

	while (currentTurn > targetTurn && undoCount < 10) {
		// Safety limit
		const result = await undoLastMove(page, config);

		if (!result.success) {
			return {
				success: false,
				strategy: result.strategy,
				message: `Failed to undo to turn ${targetTurn} after ${undoCount} attempts`
			};
		}

		currentTurn = await getCurrentTurn(page);
		undoCount++;

		// Small delay between undos
		await page.waitForTimeout(500);
	}

	if (currentTurn === targetTurn) {
		return {
			success: true,
			strategy: UndoStrategy.BUTTON, // Assuming button strategy worked
			message: `Successfully undid ${undoCount} moves to reach turn ${targetTurn}`
		};
	} else {
		return {
			success: false,
			strategy: UndoStrategy.BUTTON,
			message: `Could not reach target turn ${targetTurn}, stopped at turn ${currentTurn}`
		};
	}
}

/**
 * Get the current turn number from the game interface
 */
async function getCurrentTurn(page: Page): Promise<number> {
	const turnSelectors = ['.turn-counter', '.current-turn', '#turn-number', '[data-testid="turn"]'];

	for (const selector of turnSelectors) {
		try {
			const element = await page.$(selector);
			if (element) {
				const text = await element.textContent();
				const match = text?.match(/(\d+)/);
				if (match) {
					return parseInt(match[1]);
				}
			}
		} catch (error) {
			// Try next selector
		}
	}

	// Fallback: assume turn 1 if we can't determine
	return 1;
}

/**
 * Get the currently selected word from the display
 */
export async function getCurrentWord(page: Page): Promise<string | null> {
	for (const selector of SELECTORS.wordDisplay) {
		try {
			const element = await page.$(selector);
			if (element) {
				const word = await element.textContent();
				return word?.trim() || null;
			}
		} catch (error) {
			// Try next selector
		}
	}

	return null;
}

/**
 * Check if the game is ready for interaction
 */
export async function isGameReady(page: Page): Promise<boolean> {
	try {
		// Check if at least one tile is present and clickable
		const firstTileSelector = SELECTORS.tiles[0].replace('{row}', '0').replace('{col}', '0');
		const element = await page.$(firstTileSelector);
		return element !== null;
	} catch (error) {
		return false;
	}
}

/**
 * Wait for the game to be ready for interaction
 */
export async function waitForGameReady(
	page: Page,
	config: InteractionConfig = DEFAULT_CONFIG
): Promise<void> {
	const startTime = Date.now();

	while (Date.now() - startTime < config.timeout) {
		if (await isGameReady(page)) {
			return;
		}
		await page.waitForTimeout(500);
	}

	throw new Error('Game did not become ready within timeout period');
}
