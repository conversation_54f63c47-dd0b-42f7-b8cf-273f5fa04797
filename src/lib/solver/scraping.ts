/**
 * Board scraping utilities for the Letters game website
 *
 * This module handles extracting the current game state from the Letters
 * website DOM using Playwright automation.
 */

import type { Page } from '@playwright/test';
import { Board } from '../models/Board';
import { Tile } from '../models/Tile';
import { GAME_CONFIG } from '../types';

/**
 * Raw tile data extracted from the website
 */
interface RawTileData {
	row: number;
	col: number;
	letter: string;
	letterMult: number;
	wordMult: number;
}

/**
 * Configuration for scraping behavior
 */
interface ScrapingConfig {
	/** Maximum time to wait for elements (ms) */
	timeout: number;
	/** Delay between retries (ms) */
	retryDelay: number;
	/** Maximum number of retry attempts */
	maxRetries: number;
	/** Whether to take screenshots on failure */
	debugScreenshots: boolean;
}

/**
 * Default scraping configuration
 */
const DEFAULT_CONFIG: ScrapingConfig = {
	timeout: 10000,
	retryDelay: 1000,
	maxRetries: 3,
	debugScreenshots: false
};

/**
 * Possible selectors for game board elements (in order of preference)
 */
const BOARD_SELECTORS = [
	'.game-board',
	'#game-board',
	'.letters-grid',
	'.board-container',
	'[data-testid="game-board"]'
];

/**
 * Possible selectors for individual tiles
 */
const TILE_SELECTORS = [
	'.tile',
	'.letter-tile',
	'.game-tile',
	'.board-tile',
	'[data-testid="tile"]'
];

/**
 * Extract current board state from the Letters website
 */
export async function scrapeBoard(
	page: Page,
	config: ScrapingConfig = DEFAULT_CONFIG
): Promise<Board> {
	let lastError: Error | null = null;

	for (let attempt = 0; attempt < config.maxRetries; attempt++) {
		try {
			console.log(`[scrapeBoard] Attempt ${attempt + 1}/${config.maxRetries}`);

			// Wait for page to be ready
			await waitForGameReady(page, config);

			// Extract tile data using multiple strategies
			const tileData = await extractTileData(page, config);

			// Validate extracted data
			validateTileData(tileData);

			// Convert to Board object
			const board = createBoardFromTileData(tileData);

			console.log(`[scrapeBoard] Successfully scraped board`);
			return board;
		} catch (error) {
			lastError = error as Error;
			console.warn(`[scrapeBoard] Attempt ${attempt + 1} failed:`, error);

			if (config.debugScreenshots) {
				await page.screenshot({
					path: `debug-scrape-attempt-${attempt + 1}.png`,
					fullPage: true
				});
			}

			if (attempt < config.maxRetries - 1) {
				await page.waitForTimeout(config.retryDelay);
			}
		}
	}

	throw new Error(
		`Failed to scrape board after ${config.maxRetries} attempts. Last error: ${lastError?.message}`
	);
}

/**
 * Wait for the game to be ready for interaction
 */
async function waitForGameReady(page: Page, config: ScrapingConfig): Promise<void> {
	// Try to find the game board with multiple selectors
	let boardFound = false;

	for (const selector of BOARD_SELECTORS) {
		try {
			await page.waitForSelector(selector, { timeout: config.timeout / BOARD_SELECTORS.length });
			boardFound = true;
			console.log(`[waitForGameReady] Found board with selector: ${selector}`);
			break;
		} catch (error) {
			// Try next selector
		}
	}

	if (!boardFound) {
		throw new Error(`Game board not found. Tried selectors: ${BOARD_SELECTORS.join(', ')}`);
	}

	// Wait for any loading animations to complete
	await page.waitForTimeout(1000);

	// Ensure JavaScript has finished loading
	await page.waitForFunction(() => document.readyState === 'complete');
}

/**
 * Extract tile data from the page using multiple strategies
 */
async function extractTileData(page: Page, config: ScrapingConfig): Promise<RawTileData[]> {
	// Strategy 1: Try data attributes approach
	try {
		const dataAttributeResult = await extractTileDataByAttributes(page);
		if (dataAttributeResult.length === 25) {
			console.log('[extractTileData] Successfully used data attributes strategy');
			return dataAttributeResult;
		}
	} catch (error) {
		console.warn('[extractTileData] Data attributes strategy failed:', error);
	}

	// Strategy 2: Try CSS grid position approach
	try {
		const gridPositionResult = await extractTileDataByGridPosition(page);
		if (gridPositionResult.length === 25) {
			console.log('[extractTileData] Successfully used grid position strategy');
			return gridPositionResult;
		}
	} catch (error) {
		console.warn('[extractTileData] Grid position strategy failed:', error);
	}

	// Strategy 3: Try DOM traversal approach
	try {
		const domTraversalResult = await extractTileDataByDOMTraversal(page);
		if (domTraversalResult.length === 25) {
			console.log('[extractTileData] Successfully used DOM traversal strategy');
			return domTraversalResult;
		}
	} catch (error) {
		console.warn('[extractTileData] DOM traversal strategy failed:', error);
	}

	throw new Error('All tile extraction strategies failed');
}

/**
 * Extract tiles using data attributes (preferred method)
 */
async function extractTileDataByAttributes(page: Page): Promise<RawTileData[]> {
	return await page.evaluate(() => {
		const tiles: RawTileData[] = [];

		for (let row = 0; row < 5; row++) {
			for (let col = 0; col < 5; col++) {
				// Try multiple attribute patterns
				const selectors = [
					`[data-row="${row}"][data-col="${col}"]`,
					`[data-position="${row}-${col}"]`,
					`[data-tile-row="${row}"][data-tile-col="${col}"]`
				];

				let tileElement: Element | null = null;
				for (const selector of selectors) {
					tileElement = document.querySelector(selector);
					if (tileElement) break;
				}

				if (tileElement) {
					const letter = extractLetterFromElement(tileElement);
					const { letterMult, wordMult } = extractMultipliersFromElement(tileElement);

					tiles.push({ row, col, letter, letterMult, wordMult });
				}
			}
		}

		return tiles;
	});
}

/**
 * Extract tiles using CSS grid position
 */
async function extractTileDataByGridPosition(page: Page): Promise<RawTileData[]> {
	return await page.evaluate((tileSelectors: string[]) => {
		const tiles: RawTileData[] = [];

		// Find all tile elements
		let tileElements: Element[] = [];
		for (const selector of tileSelectors) {
			tileElements = Array.from(document.querySelectorAll(selector));
			if (tileElements.length > 0) break;
		}

		if (tileElements.length !== 25) {
			throw new Error(`Expected 25 tiles, found ${tileElements.length}`);
		}

		// Extract position from CSS grid or element order
		tileElements.forEach((element, index) => {
			const row = Math.floor(index / 5);
			const col = index % 5;

			const letter = extractLetterFromElement(element);
			const { letterMult, wordMult } = extractMultipliersFromElement(element);

			tiles.push({ row, col, letter, letterMult, wordMult });
		});

		return tiles;
	}, TILE_SELECTORS);
}

/**
 * Extract tiles by traversing DOM structure
 */
async function extractTileDataByDOMTraversal(page: Page): Promise<RawTileData[]> {
	return await page.evaluate((boardSelectors: string[]) => {
		const tiles: RawTileData[] = [];

		// Find board container
		let boardElement: Element | null = null;
		for (const selector of boardSelectors) {
			boardElement = document.querySelector(selector);
			if (boardElement) break;
		}

		if (!boardElement) {
			throw new Error('Board container not found');
		}

		// Find rows or direct tile children
		const rows = boardElement.querySelectorAll('.row, .board-row, tr');

		if (rows.length === 5) {
			// Row-based structure
			rows.forEach((row, rowIndex) => {
				const cells = row.querySelectorAll('.tile, .cell, .letter, td');
				cells.forEach((cell, colIndex) => {
					if (colIndex < 5) {
						const letter = extractLetterFromElement(cell);
						const { letterMult, wordMult } = extractMultipliersFromElement(cell);
						tiles.push({ row: rowIndex, col: colIndex, letter, letterMult, wordMult });
					}
				});
			});
		} else {
			// Flat structure - assume 5x5 grid order
			const allTiles = boardElement.querySelectorAll('.tile, .cell, .letter');
			allTiles.forEach((tile, index) => {
				if (index < 25) {
					const row = Math.floor(index / 5);
					const col = index % 5;
					const letter = extractLetterFromElement(tile);
					const { letterMult, wordMult } = extractMultipliersFromElement(tile);
					tiles.push({ row, col, letter, letterMult, wordMult });
				}
			});
		}

		return tiles;
	}, BOARD_SELECTORS);
}

/**
 * Validate that extracted tile data is complete and valid
 */
function validateTileData(tileData: RawTileData[]): void {
	if (tileData.length !== 25) {
		throw new Error(`Expected 25 tiles, got ${tileData.length}`);
	}

	// Check that all positions are covered
	const positions = new Set<string>();
	for (const tile of tileData) {
		const key = `${tile.row},${tile.col}`;
		if (positions.has(key)) {
			throw new Error(`Duplicate position found: ${key}`);
		}
		positions.add(key);

		// Validate tile data
		if (tile.row < 0 || tile.row >= 5 || tile.col < 0 || tile.col >= 5) {
			throw new Error(`Invalid position: [${tile.row}, ${tile.col}]`);
		}

		if (!tile.letter || tile.letter.length !== 1) {
			throw new Error(`Invalid letter at [${tile.row}, ${tile.col}]: "${tile.letter}"`);
		}

		if (tile.letterMult < 1 || tile.letterMult > 3 || tile.wordMult < 1 || tile.wordMult > 3) {
			throw new Error(
				`Invalid multipliers at [${tile.row}, ${tile.col}]: L${tile.letterMult}×W${tile.wordMult}`
			);
		}
	}
}

/**
 * Convert raw tile data to Board object
 */
function createBoardFromTileData(tileData: RawTileData[]): Board {
	const tiles: Tile[][] = [];

	// Initialize empty grid
	for (let row = 0; row < GAME_CONFIG.BOARD_SIZE; row++) {
		tiles[row] = [];
	}

	// Fill grid with tile data
	for (const data of tileData) {
		const tile = new Tile(
			data.letter,
			data.row as 0 | 1 | 2 | 3 | 4,
			data.col as 0 | 1 | 2 | 3 | 4,
			data.letterMult as 1 | 2 | 3,
			data.wordMult as 1 | 2 | 3
		);
		tiles[data.row][data.col] = tile;
	}

	return new Board(tiles);
}

/**
 * Helper functions that run in browser context
 * These are injected into page.evaluate() calls
 */

// Add helper functions to global scope for browser evaluation
declare global {
	function extractLetterFromElement(element: Element): string;
	function extractMultipliersFromElement(element: Element): {
		letterMult: number;
		wordMult: number;
	};
}

/**
 * Inject helper functions into page context
 */
export async function injectHelperFunctions(page: Page): Promise<void> {
	await page.addInitScript(() => {
		// Extract letter from tile element
		window.extractLetterFromElement = function (element: Element): string {
			// Try multiple approaches to get the letter
			const strategies = [
				() => element.textContent?.trim() || '',
				() => element.getAttribute('data-letter') || '',
				() => (element as HTMLElement).innerText?.trim() || '',
				() => element.querySelector('.letter')?.textContent?.trim() || '',
				() => element.querySelector('.tile-letter')?.textContent?.trim() || ''
			];

			for (const strategy of strategies) {
				const result = strategy();
				if (result && result.length === 1 && /[A-Z]/i.test(result)) {
					return result.toUpperCase();
				}
			}

			throw new Error(
				`Could not extract letter from element: ${element.outerHTML.substring(0, 100)}`
			);
		};

		// Extract multipliers from tile element
		window.extractMultipliersFromElement = function (element: Element): {
			letterMult: number;
			wordMult: number;
		} {
			let letterMult = 1;
			let wordMult = 1;

			// Strategy 1: Data attributes
			const dataLetterMult =
				element.getAttribute('data-letter-mult') || element.getAttribute('data-letter-multiplier');
			const dataWordMult =
				element.getAttribute('data-word-mult') || element.getAttribute('data-word-multiplier');

			if (dataLetterMult) letterMult = parseInt(dataLetterMult) || 1;
			if (dataWordMult) wordMult = parseInt(dataWordMult) || 1;

			// Strategy 2: CSS classes
			const classList = element.classList;

			// Letter multipliers
			if (classList.contains('letter-2x') || classList.contains('letter-double')) letterMult = 2;
			if (classList.contains('letter-3x') || classList.contains('letter-triple')) letterMult = 3;

			// Word multipliers
			if (classList.contains('word-2x') || classList.contains('word-double')) wordMult = 2;
			if (classList.contains('word-3x') || classList.contains('word-triple')) wordMult = 3;

			// Strategy 3: Look for multiplier indicators in child elements
			const multiplierElements = element.querySelectorAll('.multiplier, .mult, .bonus');
			for (const mult of multiplierElements) {
				const text = mult.textContent?.toLowerCase() || '';
				if (text.includes('2x') && text.includes('letter')) letterMult = 2;
				if (text.includes('3x') && text.includes('letter')) letterMult = 3;
				if (text.includes('2x') && text.includes('word')) wordMult = 2;
				if (text.includes('3x') && text.includes('word')) wordMult = 3;
			}

			// Strategy 4: Background color analysis (common pattern)
			const computedStyle = window.getComputedStyle(element);
			const bgColor = computedStyle.backgroundColor;

			// Common color patterns (these would need to be adjusted based on actual site)
			if (bgColor.includes('lightblue') || bgColor.includes('cyan')) letterMult = 2;
			if (bgColor.includes('blue') || bgColor.includes('navy')) letterMult = 3;
			if (bgColor.includes('pink') || bgColor.includes('lightcoral')) wordMult = 2;
			if (bgColor.includes('red') || bgColor.includes('crimson')) wordMult = 3;

			return { letterMult, wordMult };
		};
	});
}

/**
 * Create a test board for development/testing when real scraping isn't available
 */
export function createTestBoard(): Board {
	console.warn('[scrapeBoard] Using test board - replace with real scraping in production');

	// Create a realistic test board
	const testTiles: Tile[][] = [];
	const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXY';

	for (let row = 0; row < GAME_CONFIG.BOARD_SIZE; row++) {
		testTiles[row] = [];
		for (let col = 0; col < GAME_CONFIG.BOARD_SIZE; col++) {
			const letterIndex = row * 5 + col;
			const letter = letters[letterIndex];

			// Add some multipliers for testing
			let letterMult: 1 | 2 | 3 = 1;
			let wordMult: 1 | 2 | 3 = 1;

			// Corner positions get word multipliers
			if ((row === 0 || row === 4) && (col === 0 || col === 4)) {
				wordMult = 2;
			}

			// Some positions get letter multipliers
			if ((row === 1 && col === 1) || (row === 3 && col === 3)) {
				letterMult = 3;
			}

			testTiles[row][col] = new Tile(
				letter,
				row as 0 | 1 | 2 | 3 | 4,
				col as 0 | 1 | 2 | 3 | 4,
				letterMult,
				wordMult
			);
		}
	}

	return new Board(testTiles);
}
